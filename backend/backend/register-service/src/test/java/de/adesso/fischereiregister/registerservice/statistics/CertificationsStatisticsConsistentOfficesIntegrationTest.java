package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.certifications_statistics.eventhandling.CertificationsStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = RegisterServiceApplication.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CertificationsStatisticsConsistentOfficesIntegrationTest {

    public static final String PARAM_YEAR = "year";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final String TEST_OFFICE_1 = "TestOffice1";
    private static final String TEST_OFFICE_2 = "TestOffice2";
    private static final String TEST_OFFICE_3 = "TestOffice3";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    @Autowired
    private CertificationsStatisticsViewEventHandler eventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create certifications for current year - Office1 and Office2
        QualificationsProofCreatedEvent event1 = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_OFFICE_1, CURRENT_YEAR);
        QualificationsProofCreatedEvent event2 = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_OFFICE_2, CURRENT_YEAR);
        
        // Create certifications for previous year - Office2 and Office3 (different set)
        QualificationsProofCreatedEvent event3 = createCertificationEvent(TEST_FEDERAL_STATE_HH, TEST_OFFICE_2, PREVIOUS_YEAR);
        QualificationsProofCreatedEvent event4 = createCertificationEvent(TEST_FEDERAL_STATE_HH, TEST_OFFICE_3, PREVIOUS_YEAR);

        // Process events
        eventHandler.on(event1);
        eventHandler.on(event2);
        eventHandler.on(event3);
        eventHandler.on(event4);
    }

    private QualificationsProofCreatedEvent createCertificationEvent(String federalState, String office, int year) {
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setFederalState(federalState);
        qualificationsProof.setIssuedBy(office);
        qualificationsProof.setPassedOn(LocalDate.of(year, 1, 15));
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        qualificationsProof.setFishingCertificateId("CERT-" + UUID.randomUUID());

        return new QualificationsProofCreatedEvent(
                UUID.randomUUID(),
                qualificationsProof,
                new Person()
        );
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with multiple years
            Verify that all years share the same set of offices, with zero amounts for missing data.
            """)
    void callGetCertificationsStatisticsWithConsistentOfficesAcrossYears() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$", hasSize(2))) // Two years
                
                // Verify current year has all three offices
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR))
                .andExpect(jsonPath("$[0].data", hasSize(3))) // All three offices
                .andExpect(jsonPath("$[0].data[0].issuer").value(TEST_OFFICE_1))
                .andExpect(jsonPath("$[0].data[0].amount").value(1)) // Office1 has data
                .andExpect(jsonPath("$[0].data[1].issuer").value(TEST_OFFICE_2))
                .andExpect(jsonPath("$[0].data[1].amount").value(1)) // Office2 has data
                .andExpect(jsonPath("$[0].data[2].issuer").value(TEST_OFFICE_3))
                .andExpect(jsonPath("$[0].data[2].amount").value(0)) // Office3 has zero (no data for current year)
                
                // Verify previous year also has all three offices
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR))
                .andExpect(jsonPath("$[1].data", hasSize(3))) // All three offices
                .andExpect(jsonPath("$[1].data[0].issuer").value(TEST_OFFICE_1))
                .andExpect(jsonPath("$[1].data[0].amount").value(0)) // Office1 has zero (no data for previous year)
                .andExpect(jsonPath("$[1].data[1].issuer").value(TEST_OFFICE_2))
                .andExpect(jsonPath("$[1].data[1].amount").value(1)) // Office2 has data
                .andExpect(jsonPath("$[1].data[2].issuer").value(TEST_OFFICE_3))
                .andExpect(jsonPath("$[1].data[2].amount").value(1)); // Office3 has data
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications for single year
            Verify that even single year results include all offices found in the dataset.
            """)
    void callGetCertificationsStatisticsForSingleYearWithAllOffices() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$", hasSize(1))) // One year
                
                // Verify current year has all three offices (even though Office3 has no data for this year)
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR))
                .andExpect(jsonPath("$[0].data", hasSize(3))) // All three offices
                .andExpect(jsonPath("$[0].data[0].issuer").value(TEST_OFFICE_1))
                .andExpect(jsonPath("$[0].data[0].amount").value(1)) // Office1 has data
                .andExpect(jsonPath("$[0].data[1].issuer").value(TEST_OFFICE_2))
                .andExpect(jsonPath("$[0].data[1].amount").value(1)) // Office2 has data
                .andExpect(jsonPath("$[0].data[2].issuer").value(TEST_OFFICE_3))
                .andExpect(jsonPath("$[0].data[2].amount").value(0)); // Office3 has zero (no data for current year)
    }
}
